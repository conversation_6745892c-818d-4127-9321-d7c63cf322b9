{"packages": [{"name": "firebase/php-jwt", "version": "v6.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "ea7dda77098b96e666c5ef382452f94841e439cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/ea7dda77098b96e666c5ef382452f94841e439cd", "reference": "ea7dda77098b96e666c5ef382452f94841e439cd", "shasum": ""}, "require": {"php": "^7.1||^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.5||^7.4", "phpspec/prophecy-phpunit": "^1.1", "phpunit/phpunit": "^7.5||^9.5", "psr/cache": "^1.0||^2.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "time": "2022-12-19T17:10:46+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.3.2"}, "install-path": "../firebase/php-jwt"}, {"name": "rmccue/requests", "version": "v2.0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/WordPress/Requests.git", "reference": "b717f1d2f4ef7992ec0c127747ed8b7e170c2f49"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/WordPress/Requests/zipball/b717f1d2f4ef7992ec0c127747ed8b7e170c2f49", "reference": "b717f1d2f4ef7992ec0c127747ed8b7e170c2f49", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.6"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7", "php-parallel-lint/php-console-highlighter": "^0.5.0", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0", "requests/test-server": "dev-main", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.6", "wp-coding-standards/wpcs": "^2.0", "yoast/phpunit-polyfills": "^1.0.0"}, "time": "2022-10-11T08:15:28+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["library/Deprecated.php"], "psr-4": {"WpOrg\\Requests\\": "src/"}, "classmap": ["library/Requests.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["ISC"], "authors": [{"name": "<PERSON>", "homepage": "https://rmccue.io/"}, {"name": "<PERSON>", "homepage": "https://github.com/schlessera"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl"}, {"name": "Contributors", "homepage": "https://github.com/WordPress/Requests/graphs/contributors"}], "description": "A HTTP library written in PHP, for human beings.", "homepage": "https://requests.ryanmccue.info/", "keywords": ["curl", "fsockopen", "http", "idna", "ipv6", "iri", "sockets"], "support": {"docs": "https://requests.ryanmccue.info/", "issues": "https://github.com/WordPress/Requests/issues", "source": "https://github.com/WordPress/Requests"}, "install-path": "../rmccue/requests"}], "dev": true, "dev-package-names": []}