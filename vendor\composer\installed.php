<?php return array(
    'root' => array(
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '6437015f2f34f8b455db300b7798493535e8ce77',
        'name' => '__root__',
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '6437015f2f34f8b455db300b7798493535e8ce77',
            'dev_requirement' => false,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.3.2',
            'version' => '6.3.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'reference' => 'ea7dda77098b96e666c5ef382452f94841e439cd',
            'dev_requirement' => false,
        ),
        'rmccue/requests' => array(
            'pretty_version' => 'v2.0.5',
            'version' => '2.0.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rmccue/requests',
            'aliases' => array(),
            'reference' => 'b717f1d2f4ef7992ec0c127747ed8b7e170c2f49',
            'dev_requirement' => false,
        ),
    ),
);
